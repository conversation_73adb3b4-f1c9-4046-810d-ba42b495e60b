<?php

namespace app\common\library;

use think\Config;
use think\Log;

/**
 * 微信工具类
 * 统一管理微信小程序、公众号、开放平台的API调用
 */
class Wechat
{
    // 微信小程序相关配置
    private $miniAppId;
    private $miniAppSecret;
    
    // 微信公众号相关配置
    private $mpAppId;
    private $mpAppSecret;
    
    // 微信开放平台相关配置
    private $openAppId;
    private $openAppSecret;
    
    // 缓存键名前缀
    const CACHE_PREFIX_MINI = 'wechat_mini_access_token_';
    const CACHE_PREFIX_MP = 'wechat_mp_access_token_';
    const CACHE_PREFIX_OPEN = 'wechat_open_access_token_';
    
    // access_token 有效期（秒）- 微信官方为7200秒，这里设置为7000秒留出缓冲时间
    const ACCESS_TOKEN_EXPIRE = 7000;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initConfig();
    }

    /**
     * 初始化配置
     */
    private function initConfig()
    {
        // 获取微信小程序配置
        $miniConfig = Config::get('wxali.wx.xcx');
        if ($miniConfig) {
            $this->miniAppId = $miniConfig['appid'] ?? '';
            $this->miniAppSecret = $miniConfig['appsecret'] ?? '';
        }
        
        // 获取微信公众号配置（如果有的话）
        $mpConfig = Config::get('wxali.wx.mp');
        if ($mpConfig) {
            $this->mpAppId = $mpConfig['appid'] ?? '';
            $this->mpAppSecret = $mpConfig['appsecret'] ?? '';
        }
        
        // 获取微信开放平台配置（如果有的话）
        $openConfig = Config::get('wxali.wx.open');
        if ($openConfig) {
            $this->openAppId = $openConfig['appid'] ?? '';
            $this->openAppSecret = $openConfig['appsecret'] ?? '';
        }
    }

    /**
     * 获取微信小程序 access_token
     * @return string|false
     */
    public function getMiniAccessToken()
    {
        if (empty($this->miniAppId) || empty($this->miniAppSecret)) {
            Log::error('微信小程序配置不完整');
            return false;
        }

        $cacheKey = self::CACHE_PREFIX_MINI . $this->miniAppId;
        
        // 先从缓存中获取
        $accessToken = cache($cacheKey);
        if ($accessToken) {
            return $accessToken;
        }
       

        // 缓存中没有，重新获取
        return $this->refreshMiniAccessToken();
    }

    /**
     * 重新获取微信小程序 access_token
     * 注意：微信官方没有专门的刷新接口，这里实际上是重新获取新的access_token
     *
     * 调用时机：
     * 1. 缓存中没有access_token时
     * 2. 使用access_token调用API返回40001错误（token失效）时
     * 3. 手动强制刷新时
     *
     * @param bool $forceRefresh 是否强制刷新（清除缓存后重新获取）
     * @return string|false
     */
    public function refreshMiniAccessToken($forceRefresh = false)
    {
        if (empty($this->miniAppId) || empty($this->miniAppSecret)) {
            Log::error('微信小程序配置不完整');
            return false;
        }

        // 如果是强制刷新，先清除缓存
        if ($forceRefresh) {
            $cacheKey = self::CACHE_PREFIX_MINI . $this->miniAppId;
            cache($cacheKey, null);
            Log::info('强制刷新：已清除微信小程序access_token缓存');
        }

        $url = 'https://api.weixin.qq.com/cgi-bin/token';
        $params = [
            'grant_type' => 'client_credential',
            'appid' => $this->miniAppId,
            'secret' => $this->miniAppSecret
        ];

        $result = $this->httpGet($url, $params);
        if (!$result) {
            Log::error('获取微信小程序access_token失败：网络请求失败');
            return false;
        }

        $data = json_decode($result, true);
        if (!$data || isset($data['errcode'])) {
            $errorMsg = $data['errmsg'] ?? '未知错误';
            $errorCode = $data['errcode'] ?? 'unknown';
            Log::error("获取微信小程序access_token失败：[{$errorCode}] {$errorMsg}");
            return false;
        }

        $accessToken = $data['access_token'];
        $cacheKey = self::CACHE_PREFIX_MINI . $this->miniAppId;

        // 缓存access_token，使用微信返回的expires_in时间（通常是7200秒）
        $expiresIn = isset($data['expires_in']) ? intval($data['expires_in']) - 200 : self::ACCESS_TOKEN_EXPIRE;
        cache($cacheKey, $accessToken, $expiresIn);

        Log::info("微信小程序access_token获取成功，有效期：{$expiresIn}秒");
        return $accessToken;
    }

    /**
     * 获取微信公众号 access_token
     * @return string|false
     */
    public function getMpAccessToken()
    {
        if (empty($this->mpAppId) || empty($this->mpAppSecret)) {
            Log::error('微信公众号配置不完整');
            return false;
        }

        $cacheKey = self::CACHE_PREFIX_MP . $this->mpAppId;
        
        // 先从缓存中获取
        $accessToken = cache($cacheKey);
        if ($accessToken) {
            return $accessToken;
        }

        // 缓存中没有，重新获取
        return $this->refreshMpAccessToken();
    }

    /**
     * 重新获取微信公众号 access_token
     * @param bool $forceRefresh 是否强制刷新（清除缓存后重新获取）
     * @return string|false
     */
    public function refreshMpAccessToken($forceRefresh = false)
    {
        if (empty($this->mpAppId) || empty($this->mpAppSecret)) {
            Log::error('微信公众号配置不完整');
            return false;
        }

        // 如果是强制刷新，先清除缓存
        if ($forceRefresh) {
            $cacheKey = self::CACHE_PREFIX_MP . $this->mpAppId;
            cache($cacheKey, null);
            Log::info('强制刷新：已清除微信公众号access_token缓存');
        }

        $url = 'https://api.weixin.qq.com/cgi-bin/token';
        $params = [
            'grant_type' => 'client_credential',
            'appid' => $this->mpAppId,
            'secret' => $this->mpAppSecret
        ];

        $result = $this->httpGet($url, $params);
        if (!$result) {
            Log::error('获取微信公众号access_token失败：网络请求失败');
            return false;
        }

        $data = json_decode($result, true);
        if (!$data || isset($data['errcode'])) {
            $errorMsg = $data['errmsg'] ?? '未知错误';
            $errorCode = $data['errcode'] ?? 'unknown';
            Log::error("获取微信公众号access_token失败：[{$errorCode}] {$errorMsg}");
            return false;
        }

        $accessToken = $data['access_token'];
        $cacheKey = self::CACHE_PREFIX_MP . $this->mpAppId;

        // 缓存access_token
        $expiresIn = isset($data['expires_in']) ? intval($data['expires_in']) - 200 : self::ACCESS_TOKEN_EXPIRE;
        cache($cacheKey, $accessToken, $expiresIn);

        Log::info("微信公众号access_token获取成功，有效期：{$expiresIn}秒");
        return $accessToken;
    }

    /**
     * 获取微信开放平台 access_token
     * @return string|false
     */
    public function getOpenAccessToken()
    {
        if (empty($this->openAppId) || empty($this->openAppSecret)) {
            Log::error('微信开放平台配置不完整');
            return false;
        }

        $cacheKey = self::CACHE_PREFIX_OPEN . $this->openAppId;
        
        // 先从缓存中获取
        $accessToken = cache($cacheKey);
        if ($accessToken) {
            return $accessToken;
        }

        // 缓存中没有，重新获取
        return $this->refreshOpenAccessToken();
    }

    /**
     * 重新获取微信开放平台 access_token
     * @param bool $forceRefresh 是否强制刷新（清除缓存后重新获取）
     * @return string|false
     */
    public function refreshOpenAccessToken($forceRefresh = false)
    {
        if (empty($this->openAppId) || empty($this->openAppSecret)) {
            Log::error('微信开放平台配置不完整');
            return false;
        }

        // 如果是强制刷新，先清除缓存
        if ($forceRefresh) {
            $cacheKey = self::CACHE_PREFIX_OPEN . $this->openAppId;
            cache($cacheKey, null);
            Log::info('强制刷新：已清除微信开放平台access_token缓存');
        }

        $url = 'https://api.weixin.qq.com/cgi-bin/token';
        $params = [
            'grant_type' => 'client_credential',
            'appid' => $this->openAppId,
            'secret' => $this->openAppSecret
        ];

        $result = $this->httpGet($url, $params);
        if (!$result) {
            Log::error('获取微信开放平台access_token失败：网络请求失败');
            return false;
        }

        $data = json_decode($result, true);
        if (!$data || isset($data['errcode'])) {
            $errorMsg = $data['errmsg'] ?? '未知错误';
            $errorCode = $data['errcode'] ?? 'unknown';
            Log::error("获取微信开放平台access_token失败：[{$errorCode}] {$errorMsg}");
            return false;
        }

        $accessToken = $data['access_token'];
        $cacheKey = self::CACHE_PREFIX_OPEN . $this->openAppId;

        // 缓存access_token
        $expiresIn = isset($data['expires_in']) ? intval($data['expires_in']) - 200 : self::ACCESS_TOKEN_EXPIRE;
        cache($cacheKey, $accessToken, $expiresIn);

        Log::info("微信开放平台access_token获取成功，有效期：{$expiresIn}秒");
        return $accessToken;
    }

    /**
     * 微信小程序：通过code获取用户手机号
     * 支持access_token失效时自动重试机制
     *
     * @param string $code 前端获取的动态令牌
     * @param int $retryCount 重试次数（内部使用，防止无限递归）
     * @return array|false
     */
    public function getMiniPhoneNumber($code, $retryCount = 0)
    {
        if (empty($code)) {
            Log::error('微信小程序获取手机号失败：code参数为空');
            return false;
        }

        $accessToken = $this->getMiniAccessToken();

        if (!$accessToken) {
            Log::error('微信小程序获取手机号失败：无法获取access_token');
            return false;
        }

        $url = 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=' . $accessToken;
        $data = json_encode(['code' => $code]);

        $result = $this->httpPost($url, $data);
        if (!$result) {
            Log::error('微信小程序获取手机号失败：网络请求失败');
            return false;
        }

        $response = json_decode($result, true);

        if (!$response) {
            Log::error('微信小程序获取手机号失败：响应数据解析失败');
            return false;
        }

        // 检查是否是access_token失效错误
        if ($response['errcode'] == 40001 && $retryCount < 1) {
            Log::info('access_token失效，尝试刷新后重试');
            // 强制刷新access_token
            $this->refreshMiniAccessToken(true);
            // 递归重试一次
            return $this->getMiniPhoneNumber($code, $retryCount + 1);
        }

        if ($response['errcode'] != 0) {
            $errorMsg = $response['errmsg'] ?? '未知错误';
            $errorCode = $response['errcode'] ?? 'unknown';
            Log::error("微信小程序获取手机号失败：[{$errorCode}] {$errorMsg}");
            return false;
        }

        Log::info('微信小程序获取手机号成功');
        return $response['phone_info'];
    }

    /**
     * 通用的微信API调用方法（带自动重试机制）
     *
     * @param string $url API地址（不含access_token参数）
     * @param array $data 请求数据
     * @param string $method 请求方法 GET|POST
     * @param string $platform 平台类型 mini|mp|open
     * @param int $retryCount 重试次数
     * @return array|false
     */
    public function callWechatApi($url, $data = [], $method = 'POST', $platform = 'mini', $retryCount = 0)
    {
        // 获取对应平台的access_token
        switch ($platform) {
            case 'mini':
                $accessToken = $this->getMiniAccessToken();
                break;
            case 'mp':
                $accessToken = $this->getMpAccessToken();
                break;
            case 'open':
                $accessToken = $this->getOpenAccessToken();
                break;
            default:
                Log::error('不支持的微信平台类型：' . $platform);
                return false;
        }

        if (!$accessToken) {
            Log::error("获取{$platform}平台access_token失败");
            return false;
        }

        // 构建完整URL
        $fullUrl = $url . (strpos($url, '?') !== false ? '&' : '?') . 'access_token=' . $accessToken;

        // 发送请求
        if (strtoupper($method) === 'GET') {
            $result = $this->httpGet($fullUrl, $data);
        } else {
            $result = $this->httpPost($fullUrl, json_encode($data));
        }

        if (!$result) {
            Log::error('微信API调用失败：网络请求失败');
            return false;
        }

        $response = json_decode($result, true);
        if (!$response) {
            Log::error('微信API调用失败：响应数据解析失败');
            return false;
        }

        // 检查是否是access_token失效错误，如果是则自动重试
        if (isset($response['errcode']) && $response['errcode'] == 40001 && $retryCount < 1) {
            Log::info("{$platform}平台access_token失效，尝试刷新后重试");

            // 强制刷新对应平台的access_token
            switch ($platform) {
                case 'mini':
                    $this->refreshMiniAccessToken(true);
                    break;
                case 'mp':
                    $this->refreshMpAccessToken(true);
                    break;
                case 'open':
                    $this->refreshOpenAccessToken(true);
                    break;
            }

            // 递归重试一次
            return $this->callWechatApi($url, $data, $method, $platform, $retryCount + 1);
        }

        return $response;
    }

    /**
     * 发送GET请求
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return string|false
     */
    private function httpGet($url, $params = [])
    {
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; WechatAPI/1.0)');

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($result === false || !empty($error)) {
            Log::error('HTTP GET请求失败：' . $error);
            return false;
        }

        if ($httpCode != 200) {
            Log::error('HTTP GET请求失败：HTTP状态码 ' . $httpCode);
            return false;
        }

        return $result;
    }

    /**
     * 发送POST请求
     * @param string $url 请求URL
     * @param string $data 请求数据（JSON格式）
     * @return string|false
     */
    private function httpPost($url, $data = '')
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; WechatAPI/1.0)');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($result === false || !empty($error)) {
            Log::error('HTTP POST请求失败：' . $error);
            return false;
        }

        if ($httpCode != 200) {
            Log::error('HTTP POST请求失败：HTTP状态码 ' . $httpCode);
            return false;
        }

        return $result;
    }

    /**
     * 清除指定平台的access_token缓存
     * @param string $platform 平台类型：mini/mp/open
     * @return bool
     */
    public function clearAccessTokenCache($platform = 'mini')
    {
        switch ($platform) {
            case 'mini':
                $cacheKey = self::CACHE_PREFIX_MINI . $this->miniAppId;
                break;
            case 'mp':
                $cacheKey = self::CACHE_PREFIX_MP . $this->mpAppId;
                break;
            case 'open':
                $cacheKey = self::CACHE_PREFIX_OPEN . $this->openAppId;
                break;
            default:
                return false;
        }

        cache($cacheKey, null);
        Log::info("已清除{$platform}平台的access_token缓存");
        return true;
    }

    /**
     * 获取微信小程序配置信息
     * @return array
     */
    public function getMiniConfig()
    {
        return [
            'appid' => $this->miniAppId,
            'appsecret' => $this->miniAppSecret ? '***' : '' // 不返回真实密钥
        ];
    }

    /**
     * 获取微信公众号配置信息
     * @return array
     */
    public function getMpConfig()
    {
        return [
            'appid' => $this->mpAppId,
            'appsecret' => $this->mpAppSecret ? '***' : '' // 不返回真实密钥
        ];
    }

    /**
     * 获取微信开放平台配置信息
     * @return array
     */
    public function getOpenConfig()
    {
        return [
            'appid' => $this->openAppId,
            'appsecret' => $this->openAppSecret ? '***' : '' // 不返回真实密钥
        ];
    }
}
