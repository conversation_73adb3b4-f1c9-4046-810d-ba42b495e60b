# 🏗️ 项目架构设计

## 📋 系统概览

**项目名称**: 湖南沈总地锁小程序  
**系统类型**: 智能停车地锁管理系统  
**架构模式**: 前后端分离 + 微信小程序客户端  
**设计理念**: 模块化、可扩展、高可用

## 🎯 业务架构

### 核心业务领域
1. **用户管理域**
   - 用户注册/登录
   - 用户信息管理
   - 权限控制

2. **商店管理域**
   - 商店信息管理
   - 商店地理位置
   - 商店状态监控

3. **地锁管理域**
   - 地锁设备管理
   - 地锁状态监控
   - 地锁控制指令

4. **租赁服务域**
   - 租赁订单管理
   - 计费规则
   - 租赁历史

5. **套餐管理域**
   - 套餐配置
   - 价格策略
   - 优惠活动

6. **支付服务域**
   - 支付接口集成
   - 订单支付
   - 退款处理

## 🏛️ 技术架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   管理后台       │    │   第三方服务     │
│   (前端客户端)   │    │   (FastAdmin)   │    │   (微信/支付等)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ HTTPS/API             │ HTTP/HTTPS           │ API调用
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   应用服务层     │
                    │   (ThinkPHP)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据访问层     │
                    │   (ORM/DAO)     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据存储层     │
                    │   (MySQL)       │
                    └─────────────────┘
```

### 分层架构

#### 1. 表现层 (Presentation Layer)
- **微信小程序**: 用户交互界面
- **管理后台**: FastAdmin 管理界面
- **API接口**: RESTful API 服务

#### 2. 业务逻辑层 (Business Logic Layer)
- **控制器层**: 处理HTTP请求和响应
- **服务层**: 业务逻辑封装
- **领域模型**: 业务实体和规则

#### 3. 数据访问层 (Data Access Layer)
- **模型层**: ThinkPHP ORM 模型
- **数据库抽象**: 数据库操作封装

#### 4. 数据存储层 (Data Storage Layer)
- **MySQL数据库**: 主要数据存储
- **Redis缓存**: 会话和缓存存储 (可选)
- **文件存储**: 图片和文档存储

## 🔧 模块设计

### 后端模块 (ThinkPHP)

#### 1. API模块 (`application/api/`)
```php
api/
├── controller/
│   ├── User.php          # 用户相关API
│   ├── Store.php         # 商店相关API
│   ├── Lock.php          # 地锁相关API
│   ├── Rental.php        # 租赁相关API
│   ├── Package.php       # 套餐相关API
│   └── Payment.php       # 支付相关API
├── model/
│   ├── User.php          # 用户模型
│   ├── Store.php         # 商店模型
│   ├── Lock.php          # 地锁模型
│   └── ...
└── validate/
    ├── User.php          # 用户验证器
    └── ...
```

#### 2. 管理模块 (`application/admin/`)
```php
admin/
├── controller/
│   ├── User.php          # 用户管理
│   ├── Store.php         # 商店管理
│   ├── Lock.php          # 地锁管理
│   └── ...
└── view/
    ├── user/
    ├── store/
    └── ...
```

#### 3. 公共模块 (`application/common/`)
```php
common/
├── model/               # 公共模型
├── library/            # 公共类库
├── behavior/           # 行为类
└── validate/           # 验证器
```

### 前端模块 (微信小程序)

#### 1. 主包页面
```javascript
pages/
├── index/              # 首页
│   ├── index.wxml
│   ├── index.wxss
│   ├── index.js
│   └── index.json
├── my/                 # 个人中心
└── login/              # 登录页面
```

#### 2. 分包A (核心功能)
```javascript
packageA/
├── stores/             # 商店列表
├── package/            # 套餐管理
└── rental/             # 租赁功能
```

#### 3. 分包B (扩展功能)
```javascript
packageB/
├── history/            # 历史记录
├── settings/           # 设置页面
└── help/               # 帮助中心
```

## 🔄 数据流设计

### 1. 用户操作流程
```
用户操作 → 小程序界面 → API请求 → 后端处理 → 数据库操作 → 返回结果 → 界面更新
```

### 2. 地锁控制流程
```
用户租赁 → 支付确认 → 生成订单 → 发送控制指令 → 地锁响应 → 状态更新 → 用户通知
```

### 3. 数据同步流程
```
设备状态 → 实时上报 → 后端接收 → 数据处理 → 状态更新 → 前端同步
```

## 🔐 安全架构

### 1. 认证授权
- **微信授权**: 小程序登录授权
- **Token机制**: JWT或自定义Token
- **权限控制**: RBAC权限模型

### 2. 数据安全
- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS通信
- **输入验证**: 参数验证和过滤

### 3. 接口安全
- **签名验证**: API请求签名
- **频率限制**: 接口调用限流
- **异常监控**: 异常请求监控

## 📊 性能设计

### 1. 缓存策略
- **页面缓存**: 静态页面缓存
- **数据缓存**: 热点数据缓存
- **对象缓存**: 业务对象缓存

### 2. 数据库优化
- **索引设计**: 合理的索引策略
- **查询优化**: SQL查询优化
- **读写分离**: 主从数据库 (可选)

### 3. 前端优化
- **分包加载**: 按需加载功能模块
- **图片优化**: 图片压缩和CDN
- **请求优化**: 接口合并和缓存

## 🔧 扩展性设计

### 1. 模块化设计
- **插件机制**: FastAdmin插件系统
- **服务解耦**: 微服务架构准备
- **配置管理**: 灵活的配置系统

### 2. 第三方集成
- **支付接口**: 多种支付方式支持
- **消息推送**: 多渠道消息通知
- **地图服务**: 地理位置服务集成

### 3. 部署架构
- **容器化**: Docker部署支持
- **负载均衡**: 多实例部署
- **监控告警**: 系统监控和告警

---

**设计原则**: 高内聚、低耦合、可扩展、易维护  
**最后更新**: 2025-01-29  
**版本**: v1.0 (初始设计)
