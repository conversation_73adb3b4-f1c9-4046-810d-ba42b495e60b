<?php

namespace app\common\library;

/**
 * 日志记录工具类
 * 专为ThinkPHP 5.0优化的日志记录方案
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */
class Logger
{
    /**
     * 记录信息日志
     * @param string $message 日志消息
     * @param array $data 附加数据
     * @param string $module 模块名称
     */
    public static function info($message, $data = [], $module = '')
    {
        self::writeLog($message, $data, 'info', $module);
    }
    
    /**
     * 记录错误日志
     * @param string $message 日志消息
     * @param array $data 附加数据
     * @param string $module 模块名称
     */
    public static function error($message, $data = [], $module = '')
    {
        self::writeLog($message, $data, 'error', $module);
    }
    
    /**
     * 记录警告日志
     * @param string $message 日志消息
     * @param array $data 附加数据
     * @param string $module 模块名称
     */
    public static function warning($message, $data = [], $module = '')
    {
        self::writeLog($message, $data, 'notice', $module);
    }
    
    /**
     * 记录调试日志
     * @param string $message 日志消息
     * @param array $data 附加数据
     * @param string $module 模块名称
     */
    public static function debug($message, $data = [], $module = '')
    {
        // 只在调试模式下记录
        if (config('app_debug')) {
            self::writeLog($message, $data, 'debug', $module);
        }
    }
    
    /**
     * 记录用户操作日志
     * @param string $action 操作名称
     * @param int $userId 用户ID
     * @param array $data 操作数据
     */
    public static function userAction($action, $userId, $data = [])
    {
        $logData = [
            'user_id' => $userId,
            'action' => $action,
            'ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'time' => date('Y-m-d H:i:s'),
            'data' => $data
        ];
        
        self::writeLog('用户操作', $logData, 'info', 'user_action');
    }
    
    /**
     * 记录API调用日志
     * @param string $api API名称
     * @param array $params 请求参数
     * @param array $response 响应数据
     * @param float $duration 执行时间
     */
    public static function apiCall($api, $params = [], $response = [], $duration = 0)
    {
        $logData = [
            'api' => $api,
            'method' => request()->method(),
            'params' => $params,
            'response_code' => $response['code'] ?? 0,
            'duration' => $duration . 'ms',
            'ip' => request()->ip(),
            'time' => date('Y-m-d H:i:s')
        ];
        
        // 不记录敏感信息
        if (isset($logData['params']['password'])) {
            $logData['params']['password'] = '***';
        }
        if (isset($logData['params']['token'])) {
            $logData['params']['token'] = substr($logData['params']['token'], 0, 10) . '***';
        }
        
        self::writeLog('API调用', $logData, 'info', 'api');
    }
    
    /**
     * 记录数据库操作日志
     * @param string $operation 操作类型 (insert/update/delete)
     * @param string $table 表名
     * @param array $data 操作数据
     * @param mixed $condition 条件
     */
    public static function dbOperation($operation, $table, $data = [], $condition = null)
    {
        $logData = [
            'operation' => $operation,
            'table' => $table,
            'data' => $data,
            'condition' => $condition,
            'time' => date('Y-m-d H:i:s')
        ];
        
        self::writeLog('数据库操作', $logData, 'info', 'database');
    }
    
    /**
     * 记录地锁操作日志
     * @param string $action 操作类型 (unlock/lock/status)
     * @param int $lockId 地锁ID
     * @param int $userId 用户ID
     * @param array $result 操作结果
     */
    public static function lockOperation($action, $lockId, $userId, $result = [])
    {
        $logData = [
            'action' => $action,
            'lock_id' => $lockId,
            'user_id' => $userId,
            'result' => $result,
            'ip' => request()->ip(),
            'time' => date('Y-m-d H:i:s')
        ];
        
        self::writeLog('地锁操作', $logData, 'info', 'lock');
    }
    
    /**
     * 记录支付日志
     * @param string $action 支付动作
     * @param string $orderId 订单ID
     * @param float $amount 金额
     * @param array $paymentData 支付数据
     */
    public static function payment($action, $orderId, $amount, $paymentData = [])
    {
        $logData = [
            'action' => $action,
            'order_id' => $orderId,
            'amount' => $amount,
            'payment_data' => $paymentData,
            'time' => date('Y-m-d H:i:s')
        ];
        
        self::writeLog('支付操作', $logData, 'info', 'payment');
    }
    
    /**
     * 写入日志的核心方法
     * @param string $message 消息
     * @param array $data 数据
     * @param string $level 级别
     * @param string $module 模块
     */
    private static function writeLog($message, $data, $level, $module)
    {
        // 构建日志消息
        $logMessage = $message;
        
        // 添加模块信息
        if ($module) {
            $logMessage = "[{$module}] " . $logMessage;
        }
        
        // 添加数据信息
        if (!empty($data)) {
            if (is_array($data)) {
                $logMessage .= ' - 数据:' . json_encode($data, JSON_UNESCAPED_UNICODE);
            } else {
                $logMessage .= ' - 数据:' . $data;
            }
        }
        
        // 使用trace函数记录日志
        trace($logMessage, $level);
    }
    
    /**
     * 格式化异常信息
     * @param \Exception $e 异常对象
     * @return array
     */
    public static function formatException(\Exception $e)
    {
        return [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
    }
    
    /**
     * 记录异常日志
     * @param \Exception $e 异常对象
     * @param string $context 上下文信息
     */
    public static function exception(\Exception $e, $context = '')
    {
        $exceptionData = self::formatException($e);
        $message = '异常发生';
        
        if ($context) {
            $message .= ' - 上下文:' . $context;
        }
        
        self::writeLog($message, $exceptionData, 'error', 'exception');
    }
}
