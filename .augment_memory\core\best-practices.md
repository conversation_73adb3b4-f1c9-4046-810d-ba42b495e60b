# 🎯 项目特定最佳实践

## 📋 开发规范

### PHP/ThinkPHP 最佳实践

#### 1. 代码结构
```php
// 控制器示例
<?php
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\User;

class UserController extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['login', 'register'];
    
    // 无需权限验证的接口
    protected $noNeedRight = ['profile'];
    
    /**
     * 用户登录
     */
    public function login()
    {
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        
        // 参数验证
        $validate = new \app\api\validate\User;
        if (!$validate->scene('login')->check(['mobile' => $mobile, 'code' => $code])) {
            $this->error($validate->getError());
        }
        
        // 业务逻辑
        $result = User::login($mobile, $code);
        
        $this->success('登录成功', $result);
    }
}
```

#### 2. 模型设计
```php
// 模型示例
<?php
namespace app\common\model;

use think\Model;

class User extends Model
{
    // 数据表名
    protected $table = 'user';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'status' => 'integer',
        'createtime' => 'timestamp',
        'updatetime' => 'timestamp',
    ];
    
    // 关联关系
    public function rentals()
    {
        return $this->hasMany('Rental');
    }
    
    // 业务方法
    public static function login($mobile, $code)
    {
        // 验证短信验证码
        // 查找或创建用户
        // 生成Token
        // 返回用户信息
    }
}
```

### 微信小程序最佳实践

#### 1. 页面结构
```javascript
// 页面JS示例
Page({
  data: {
    userInfo: null,
    stores: [],
    loading: false
  },

  onLoad: function(options) {
    this.checkLogin();
    this.loadStores();
  },

  // 检查登录状态
  checkLogin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },

  // 加载商店列表
  loadStores: function() {
    this.setData({ loading: true });
    
    wx.request({
      url: app.globalData.apiUrl + '/api/store/list',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            stores: res.data.data,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
        }
      }
    });
  }
});
```

#### 2. 工具函数封装
```javascript
// utils/api.js
const app = getApp();

// API请求封装
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: app.globalData.apiUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        ...options.header
      },
      success: (res) => {
        if (res.data.code === 1) {
          resolve(res.data);
        } else {
          wx.showToast({
            title: res.data.msg,
            icon: 'none'
          });
          reject(res.data);
        }
      },
      fail: reject
    });
  });
}

module.exports = {
  request
};
```

## 🔐 安全最佳实践

### 1. 输入验证
```php
// 验证器示例
<?php
namespace app\api\validate;

use think\Validate;

class User extends Validate
{
    protected $rule = [
        'mobile' => 'require|mobile',
        'code' => 'require|length:6',
        'nickname' => 'require|length:2,20',
    ];
    
    protected $message = [
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式不正确',
        'code.require' => '验证码不能为空',
        'code.length' => '验证码长度不正确',
    ];
    
    protected $scene = [
        'login' => ['mobile', 'code'],
        'register' => ['mobile', 'code', 'nickname'],
    ];
}
```

### 2. 权限控制
```php
// 权限中间件
<?php
namespace app\api\middleware;

class Auth
{
    public function handle($request, \Closure $next)
    {
        $token = $request->header('authorization');
        
        if (!$token) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }
        
        // 验证Token
        $user = User::getByToken($token);
        if (!$user) {
            return json(['code' => 0, 'msg' => 'Token无效']);
        }
        
        // 将用户信息注入请求
        $request->user = $user;
        
        return $next($request);
    }
}
```

## 📊 性能优化

### 1. 数据库优化
```sql
-- 索引设计示例
CREATE INDEX idx_user_mobile ON user(mobile);
CREATE INDEX idx_rental_user_id ON rental(user_id);
CREATE INDEX idx_rental_status_time ON rental(status, createtime);

-- 查询优化
SELECT u.*, COUNT(r.id) as rental_count 
FROM user u 
LEFT JOIN rental r ON u.id = r.user_id 
WHERE u.status = 1 
GROUP BY u.id 
ORDER BY u.createtime DESC 
LIMIT 20;
```

### 2. 缓存策略
```php
// 缓存使用示例
class StoreService
{
    public static function getStoreList($page = 1, $limit = 20)
    {
        $cacheKey = "store_list_{$page}_{$limit}";
        
        // 尝试从缓存获取
        $data = cache($cacheKey);
        if ($data !== false) {
            return $data;
        }
        
        // 从数据库查询
        $data = Store::where('status', 1)
            ->page($page, $limit)
            ->select();
        
        // 缓存5分钟
        cache($cacheKey, $data, 300);
        
        return $data;
    }
}
```

## 🔄 错误处理

### 1. 异常处理
```php
// 全局异常处理
<?php
namespace app\common\exception;

use Exception;
use think\exception\Handle;
use think\Response;

class Http extends Handle
{
    public function render(Exception $e)
    {
        // API接口异常返回JSON
        if (request()->isAjax() || strpos(request()->pathinfo(), 'api/') === 0) {
            return json([
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ]);
        }
        
        return parent::render($e);
    }
}
```

### 2. 日志记录
```php
// 日志记录示例
use think\Log;

class UserController extends Api
{
    public function login()
    {
        try {
            // 业务逻辑
            $result = User::login($mobile, $code);
            
            // 记录成功日志
            Log::info("用户登录成功: {$mobile}");
            
            $this->success('登录成功', $result);
        } catch (Exception $e) {
            // 记录错误日志
            Log::error("用户登录失败: {$mobile}, 错误: " . $e->getMessage());
            
            $this->error('登录失败');
        }
    }
}
```

## 🧪 测试规范

### 1. 单元测试
```php
// 测试示例
<?php
namespace tests\unit;

use PHPUnit\Framework\TestCase;
use app\common\model\User;

class UserTest extends TestCase
{
    public function testUserLogin()
    {
        $mobile = '13800138000';
        $code = '123456';
        
        // 模拟验证码验证
        $result = User::login($mobile, $code);
        
        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('userinfo', $result);
    }
}
```

### 2. API测试
```javascript
// 小程序测试示例
describe('API测试', () => {
  it('用户登录接口', (done) => {
    wx.request({
      url: 'http://localhost/api/user/login',
      method: 'POST',
      data: {
        mobile: '13800138000',
        code: '123456'
      },
      success: (res) => {
        expect(res.data.code).toBe(1);
        expect(res.data.data).toHaveProperty('token');
        done();
      }
    });
  });
});
```

## 📝 文档规范

### 1. API文档
```php
/**
 * 用户登录
 * @ApiTitle    (用户登录)
 * @ApiSummary  (用户登录)
 * @ApiMethod   (POST)
 * @ApiRoute    (/api/user/login)
 * @ApiParams   (name="mobile", type="string", required=true, description="手机号")
 * @ApiParams   (name="code", type="string", required=true, description="验证码")
 * @ApiReturn   ({
    "code": 1,
    "msg": "登录成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "userinfo": {
            "id": 1,
            "nickname": "用户昵称",
            "mobile": "13800138000"
        }
    }
})
 */
public function login()
{
    // 实现代码
}
```

### 2. 代码注释
```php
/**
 * 地锁控制服务
 * 
 * 负责地锁设备的远程控制，包括开锁、关锁、状态查询等功能
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-29
 */
class LockService
{
    /**
     * 远程开锁
     * 
     * @param int $lockId 地锁ID
     * @param int $userId 用户ID
     * @param string $orderId 订单ID
     * @return array 操作结果
     * @throws \Exception 当地锁不存在或状态异常时抛出异常
     */
    public static function unlock($lockId, $userId, $orderId)
    {
        // 实现代码
    }
}
```

---

**制定原则**: 安全第一、性能优化、代码规范、文档完善  
**最后更新**: 2025-01-29  
**适用版本**: v1.0+
