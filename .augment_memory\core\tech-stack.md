# 🔧 技术栈信息和开发约定

## 📊 技术栈概览

### 后端技术栈
- **主框架**: FastAdmin + ThinkPHP 5.0
- **语言版本**: PHP >= 5.4.0
- **包管理器**: Composer
- **许可证**: Apache-2.0

### 前端技术栈
- **平台**: 微信小程序
- **架构**: 主包 + 分包模式
- **开发工具**: 微信开发者工具

### 数据库
- **类型**: MySQL (基于 ThinkPHP 推测)
- **ORM**: ThinkPHP 内置 ORM

## 📦 核心依赖

### Composer 依赖 (composer.json)
```json
{
  "require": {
    "php": ">=5.4.0",
    "topthink/framework": "~5.0.24",
    "overtrue/wechat": "~3.1",
    "endroid/qr-code": "^1.9",
    "topthink/think-captcha": "^1.0",
    "mtdowling/cron-expression": "^1.2",
    "phpmailer/phpmailer": "^5.2",
    "karsonzhang/fastadmin-addons": "~1.1.4",
    "overtrue/pinyin": "~3.0",
    "phpoffice/phpspreadsheet": "^1.2"
  }
}
```

### 关键依赖说明
- **topthink/framework**: ThinkPHP 5.0 核心框架
- **overtrue/wechat**: 微信开发 SDK
- **endroid/qr-code**: 二维码生成
- **think-captcha**: 验证码功能
- **phpmailer**: 邮件发送
- **phpspreadsheet**: Excel 文件处理

## 🏗️ 项目结构

### 后端结构 (FastAdmin)
```
application/
├── admin/              # 后台管理模块
├── api/               # API接口模块
├── common/            # 公共模块
├── index/             # 前台模块
├── build.php          # 构建配置
├── command.php        # 命令行配置
├── common.php         # 公共函数
├── config.php         # 应用配置
├── database.php       # 数据库配置
├── route.php          # 路由配置
└── tags.php          # 标签配置
```

### 前端结构 (微信小程序)
```
湖南沈总地锁小程序/
├── pages/             # 主包页面
│   ├── index/         # 首页
│   └── ...
├── packageA/          # 分包A
│   ├── stores/        # 商店相关
│   ├── package/       # 套餐相关
│   └── rental/        # 租赁相关
├── packageB/          # 分包B
├── utils/             # 工具函数
├── js/               # JavaScript文件
├── image/            # 图片资源
├── app.js            # 小程序入口
├── app.json          # 小程序配置
└── app.wxss          # 全局样式
```

## 🔧 开发约定

### PHP 开发规范
- **编码标准**: PSR-4 自动加载
- **命名约定**: 
  - 类名: 大驼峰 (PascalCase)
  - 方法名: 小驼峰 (camelCase)
  - 常量: 全大写下划线分隔
- **文件结构**: 遵循 ThinkPHP 5.0 目录规范

### 微信小程序规范
- **文件命名**: 小写字母 + 连字符
- **组件结构**: .wxml, .wxss, .js, .json 四文件结构
- **分包策略**: 按功能模块分包，控制包大小

### API 接口规范
- **路径**: `/api/模块/方法`
- **返回格式**: JSON
- **状态码**: HTTP 标准状态码
- **认证**: 基于 Token 的认证机制

## 🛠️ 开发工具链

### 后端开发
- **IDE**: PhpStorm / VS Code
- **调试**: Xdebug
- **测试**: PHPUnit (可选)
- **代码质量**: PHP_CodeSniffer

### 前端开发
- **IDE**: 微信开发者工具
- **调试**: 微信开发者工具内置调试器
- **预览**: 真机预览和模拟器

### 版本控制
- **VCS**: Git
- **分支策略**: 待定义
- **提交规范**: 待定义

## 🔌 第三方集成

### 微信生态
- **微信登录**: 小程序授权登录
- **微信支付**: 小程序支付接口
- **微信消息**: 模板消息推送

### 其他服务
- **短信服务**: 阿里云/腾讯云短信
- **云存储**: 七牛/阿里云OSS/又拍云
- **邮件服务**: PHPMailer

## 📋 配置管理

### 环境配置
- **开发环境**: 本地 PHP 环境
- **测试环境**: 待配置
- **生产环境**: 待配置

### 配置文件
- **数据库**: `application/database.php`
- **应用配置**: `application/config.php`
- **路由配置**: `application/route.php`

## 🚀 构建和部署

### 构建流程
1. Composer 依赖安装
2. 数据库迁移和初始化
3. 静态资源处理
4. 小程序代码上传

### 部署检查清单
- [ ] PHP 环境版本检查
- [ ] Composer 依赖安装
- [ ] 数据库连接配置
- [ ] 文件权限设置
- [ ] 微信小程序配置
- [ ] 第三方服务配置

---

**最后更新**: 2025-01-29  
**维护者**: Augment Agent  
**版本**: v1.0 (初始化)
