# 📋 Augment Agent 记忆系统导航

## 🎯 项目概览

**项目名称**: 湖南沈总地锁小程序  
**技术栈**: PHP (FastAdmin + ThinkPHP 5.0) + 微信小程序  
**初始化时间**: 2025-01-29  
**当前状态**: 已初始化，准备开始AI辅助开发

## 📁 记忆系统结构

### 🧠 长期记忆 (core/)
- `architecture.md` - 项目架构设计和技术选型
- `patterns.md` - 成功的实现模式和代码规范
- `decisions.md` - 重要的架构决策记录
- `best-practices.md` - 项目特定最佳实践
- `tech-stack.md` - 技术栈信息和开发约定

### 📝 短期记忆 (task-logs/)
- 按日期组织的任务执行日志
- 记录每次开发会话的具体工作内容
- 包含问题解决过程和学习要点

### ⚡ 工作记忆
- `activeContext.md` - 当前任务上下文和状态
- `memory-index.md` - 记忆索引和快速导航
- `PROJECT_CONTEXT_HANDOVER.md` - 新会话上下文传递

### 📊 专业分析 (architecture_analysis/)
- `code_analysis/` - 代码质量分析
- `mermaid_diagrams/` - 系统流程图
- `optimization_recommendations/` - 优化建议

## 🚀 快速开始

### 新会话开始
```bash
"执行 augment_context_read 命令"
```

### 会话结束保存
```bash
"执行 augment_context_write 命令"
```

### 重新加载记忆
```bash
"执行 augment_reload 命令重新加载项目记忆"
```

## 📚 重要文档快速访问

- 📊 **项目交接**: [PROJECT_CONTEXT_HANDOVER.md](PROJECT_CONTEXT_HANDOVER.md)
- ⚡ **当前状态**: [activeContext.md](activeContext.md)
- 📇 **记忆索引**: [memory-index.md](memory-index.md)
- 🏗️ **系统架构**: [core/architecture.md](core/architecture.md)
- 🔧 **技术栈**: [core/tech-stack.md](core/tech-stack.md)

## 🎯 下一步行动

1. 查看 `PROJECT_CONTEXT_HANDOVER.md` 了解项目当前状态
2. 检查 `activeContext.md` 获取当前任务上下文
3. 根据需要查看相关的长期记忆文档
4. 开始AI辅助开发工作

---

**🎉 Augment Agent 记忆系统已就绪！**  
立即开始您的AI辅助开发之旅！
